<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        <?php echo app('translator')->get('sales::app.reports.title'); ?>
     <?php $__env->endSlot(); ?>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <?php if (isset($component)) { $__componentOriginal477735b45b070062c5df1d72c43d48f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal477735b45b070062c5df1d72c43d48f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.breadcrumbs.index','data' => ['name' => 'sales.reports']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'sales.reports']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $attributes = $__attributesOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__attributesOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $component = $__componentOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__componentOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
                </div>

                <div class="text-xl font-bold dark:text-white">
                    <?php echo app('translator')->get('sales::app.reports.title'); ?>
                </div>

                <div class="text-gray-600 dark:text-gray-300">
                    <?php echo app('translator')->get('sales::app.reports.description'); ?>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                <div class="flex items-center">
                    <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                        <span class="icon-target text-xl text-blue-600 dark:text-blue-400"></span>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($totalTargets ?? 0); ?></p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Total Targets</p>
                    </div>
                </div>
            </div>

            <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                <div class="flex items-center">
                    <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                        <span class="icon-user text-xl text-green-600 dark:text-green-400"></span>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($activeUsers ?? 0); ?></p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Active Sales Reps</p>
                    </div>
                </div>
            </div>

            <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                <div class="flex items-center">
                    <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                        <span class="icon-stats-up text-xl text-purple-600 dark:text-purple-400"></span>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e(number_format($avgAchievement ?? 0, 1)); ?>%</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Avg Achievement</p>
                    </div>
                </div>
            </div>

            <div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                <div class="flex items-center">
                    <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900">
                        <span class="icon-calendar text-xl text-orange-600 dark:text-orange-400"></span>
                    </div>
                    <div class="ml-4">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?php echo e($currentPeriod ?? 'N/A'); ?></p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Current Period</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Cards -->
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <!-- Target vs Actual Report -->
            <div class="group cursor-pointer rounded-lg border border-gray-200 bg-white p-6 transition-all hover:border-blue-300 hover:shadow-lg dark:border-gray-800 dark:bg-gray-900 dark:hover:border-blue-600">
                <div class="flex items-start space-x-4">
                    <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 group-hover:bg-blue-200 dark:bg-blue-900 dark:group-hover:bg-blue-800">
                        <span class="icon-stats-up text-xl text-blue-600 dark:text-blue-400"></span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Target vs Actual</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">Performance Analysis</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Compare sales targets with actual achievements across different time periods.</p>
                        <a href="<?php echo e(route('admin.sales.reports.target_vs_actual')); ?>" class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                            View Report
                            <span class="icon-arrow-right ml-1 text-xs"></span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Leaderboard Report -->
            <div class="group cursor-pointer rounded-lg border border-gray-200 bg-white p-6 transition-all hover:border-green-300 hover:shadow-lg dark:border-gray-800 dark:bg-gray-900 dark:hover:border-green-600">
                <div class="flex items-start space-x-4">
                    <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 group-hover:bg-green-200 dark:bg-green-900 dark:group-hover:bg-green-800">
                        <span class="icon-trophy text-xl text-green-600 dark:text-green-400"></span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Leaderboard</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">Top Performers</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">View top performing sales representatives based on achievement percentage.</p>
                        <a href="<?php echo e(route('admin.sales.reports.leaderboard')); ?>" class="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 dark:text-green-400 dark:hover:text-green-300">
                            View Report
                            <span class="icon-arrow-right ml-1 text-xs"></span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Forecast Report -->
            <div class="group cursor-pointer rounded-lg border border-gray-200 bg-white p-6 transition-all hover:border-purple-300 hover:shadow-lg dark:border-gray-800 dark:bg-gray-900 dark:hover:border-purple-600">
                <div class="flex items-start space-x-4">
                    <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 group-hover:bg-purple-200 dark:bg-purple-900 dark:group-hover:bg-purple-800">
                        <span class="icon-chart text-xl text-purple-600 dark:text-purple-400"></span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sales Forecast</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">Future Projections</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Analyze sales trends and forecast future performance based on historical data.</p>
                        <a href="<?php echo e(route('admin.sales.reports.forecast')); ?>" class="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-500 dark:text-purple-400 dark:hover:text-purple-300">
                            View Report
                            <span class="icon-arrow-right ml-1 text-xs"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Overview Chart -->
        <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
            <div class="mb-6 flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance Overview</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Monthly target vs achievement comparison</p>
                </div>
                <div class="flex space-x-2">
                    <button class="rounded-md bg-blue-100 px-3 py-1 text-sm font-medium text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800">
                        6M
                    </button>
                    <button class="rounded-md bg-gray-100 px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        1Y
                    </button>
                </div>
            </div>

            <div class="h-80">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- Top Performers -->
            <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Top Performers This Month</h3>
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $topPerformers ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                                        <?php echo e(substr(optional($performer->user)->name ?? 'N/A', 0, 2)); ?>

                                    </span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e(optional($performer->user)->name ?? 'N/A'); ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($performer->achievement_percentage ?? 0); ?>% achievement</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">$<?php echo e(number_format($performer->achieved_value ?? 0)); ?></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">of $<?php echo e(number_format($performer->target_value ?? 0)); ?></p>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-8">
                            <span class="icon-user text-4xl text-gray-300 dark:text-gray-600"></span>
                            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No performance data available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Achievements -->
            <div class="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-gray-900">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Recent Achievements</h3>
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $recentAchievements ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="flex items-start space-x-3">
                            <div class="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                                <span class="icon-success text-sm text-green-600 dark:text-green-400"></span>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">
                                    <?php echo e(optional($achievement->user)->name ?? 'N/A'); ?> achieved $<?php echo e(number_format($achievement->achievement_value ?? 0)); ?>

                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php echo e($achievement->achievement_date ? $achievement->achievement_date->diffForHumans() : 'N/A'); ?>

                                </p>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-8">
                            <span class="icon-trophy text-4xl text-gray-300 dark:text-gray-600"></span>
                            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No recent achievements</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('performanceChart').getContext('2d');

    // Sample data - in real implementation, this would come from the controller
    const chartData = <?php echo json_encode($chartData ?? [
        'labels' => ['Jan', 'Feb', 'Mar') ?>;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'Target',
                data: chartData.targets,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: 'Achievement',
                data: chartData.achievements,
                borderColor: 'rgb(16, 185, 129)',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Month'
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Amount ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Sales\src/resources/views/reports/index.blade.php ENDPATH**/ ?>