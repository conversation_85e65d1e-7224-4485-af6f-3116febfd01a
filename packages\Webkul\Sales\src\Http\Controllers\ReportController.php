<?php

namespace Webkul\Sales\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Sales\Repositories\SalesAchievementRepository;
use Webkul\Sales\Repositories\SalesTargetRepository;
use Webkul\User\Repositories\UserRepository;

class ReportController extends Controller
{
    public function __construct(
        protected SalesTargetRepository $salesTargetRepository,
        protected SalesAchievementRepository $salesAchievementRepository,
        protected UserRepository $userRepository
    ) {}

    /**
     * Display reports index page.
     */
    public function index(): View
    {
        // Get summary statistics for the dashboard
        $totalTargets = $this->salesTargetRepository->count();
        $activeUsers = $this->userRepository->count();

        // Calculate average achievement percentage
        $avgAchievement = $this->salesTargetRepository->getModel()
            ->selectRaw('AVG(CASE WHEN target_value > 0 THEN (achieved_value / target_value) * 100 ELSE 0 END) as avg_achievement')
            ->value('avg_achievement') ?? 0;

        $currentPeriod = 'Q'.ceil(date('n') / 3).' '.date('Y');

        // Get top performers for the current month
        $topPerformers = $this->salesTargetRepository
            ->getModel()
            ->with('user')
            ->where('start_date', '<=', Carbon::now())
            ->where('end_date', '>=', Carbon::now())
            ->orderBy('achievement_percentage', 'desc')
            ->limit(5)
            ->get();

        // Get recent achievements - handle case where table might not exist yet
        $recentAchievements = collect();
        try {
            $recentAchievements = $this->salesAchievementRepository
                ->getModel()
                ->with('user')
                ->orderBy('achievement_date', 'desc')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            // Table might not exist yet, use empty collection
            $recentAchievements = collect();
        }

        // Generate chart data for the last 6 months
        $chartData = $this->generateChartData();

        return view('sales::reports.index', compact(
            'totalTargets',
            'activeUsers',
            'avgAchievement',
            'currentPeriod',
            'topPerformers',
            'recentAchievements',
            'chartData'
        ));
    }

    /**
     * Target vs Actual report.
     */
    public function targetVsActual(Request $request): View
    {
        $filters = $request->only(['financial_year', 'period_type', 'user_id']);

        $reportData = $this->salesTargetRepository->getPerformanceSummary($filters);
        $users = $this->userRepository->all(['id', 'name']);

        return view('sales::reports.target-vs-actual', compact('reportData', 'filters', 'users'));
    }

    /**
     * Leaderboard report.
     */
    public function leaderboard(Request $request): View
    {
        $financialYear = $request->get('financial_year');

        $topPerformers = $this->salesTargetRepository
            ->with('user')
            ->when($financialYear, function ($query, $year) {
                return $query->where('financial_year', $year);
            })
            ->orderBy('achievement_percentage', 'desc')
            ->limit(20)
            ->get();

        return view('sales::reports.leaderboard', compact('topPerformers'));
    }

    /**
     * Forecast report.
     */
    public function forecast(): View
    {
        // Implementation for forecast report
        return view('sales::reports.forecast');
    }

    /**
     * Export reports.
     */
    public function export(string $type): Response
    {
        // Implementation for export functionality
        return response()->json(['message' => 'Export functionality coming soon']);
    }

    /**
     * Generate chart data for performance overview.
     */
    private function generateChartData(): array
    {
        $months = [];
        $targets = [];
        $achievements = [];

        // Get data for the last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthStart = $date->copy()->startOfMonth();
            $monthEnd = $date->copy()->endOfMonth();

            $months[] = $date->format('M');

            try {
                // Get targets for this month
                $monthlyTargets = $this->salesTargetRepository
                    ->getModel()
                    ->where('start_date', '<=', $monthEnd)
                    ->where('end_date', '>=', $monthStart)
                    ->sum('target_value');

                // Get achievements for this month
                $monthlyAchievements = $this->salesTargetRepository
                    ->getModel()
                    ->where('start_date', '<=', $monthEnd)
                    ->where('end_date', '>=', $monthStart)
                    ->sum('achieved_value');

                $targets[] = (float) ($monthlyTargets ?? 0);
                $achievements[] = (float) ($monthlyAchievements ?? 0);
            } catch (\Exception $e) {
                // Handle case where data might not be available
                $targets[] = 0;
                $achievements[] = 0;
            }
        }

        return [
            'labels'       => $months,
            'targets'      => $targets,
            'achievements' => $achievements,
        ];
    }
}
