<x-admin::layouts>
    <x-slot:title>
        Sales Performance
    </x-slot>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <x-admin::breadcrumbs name="sales.performance" />
                </div>

                <div class="text-xl font-bold dark:text-white">
                    Sales Performance
                </div>
            </div>
        </div>

        <x-admin::layouts.filter>
            <x-slot:form>
                <form method="GET" action="{{ route('admin.sales.performance.index') }}">
                    <div class="flex gap-[16px] items-center">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.performance.filters.financial-year') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="financial_year"
                                :value="request('financial_year')"
                                placeholder="{{ __('sales::app.performance.filters.all-years') }}"
                            >
                                <option value="">{{ __('sales::app.performance.filters.all-years') }}</option>
                                @foreach($financialYears as $year)
                                    <option value="{{ $year }}" {{ request('financial_year') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endforeach
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.performance.filters.period-type') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="period_type"
                                :value="request('period_type')"
                                placeholder="{{ __('sales::app.performance.filters.all-periods') }}"
                            >
                                <option value="">{{ __('sales::app.performance.filters.all-periods') }}</option>
                                <option value="annual" {{ request('period_type') == 'annual' ? 'selected' : '' }}>{{ __('sales::app.performance.filters.annual') }}</option>
                                <option value="half_yearly" {{ request('period_type') == 'half_yearly' ? 'selected' : '' }}>{{ __('sales::app.performance.filters.half-yearly') }}</option>
                                <option value="quarterly" {{ request('period_type') == 'quarterly' ? 'selected' : '' }}>{{ __('sales::app.performance.filters.quarterly') }}</option>
                                <option value="monthly" {{ request('period_type') == 'monthly' ? 'selected' : '' }}>{{ __('sales::app.performance.filters.monthly') }}</option>
                                <option value="custom" {{ request('period_type') == 'custom' ? 'selected' : '' }}>{{ __('sales::app.performance.filters.custom') }}</option>
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <div id="custom_date_range" class="flex gap-[16px] items-center" style="display: none;">
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    {{ __('sales::app.performance.filters.custom-from') }}
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="date"
                                    name="custom_from"
                                    :value="request('custom_from')"
                                />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    {{ __('sales::app.performance.filters.custom-to') }}
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="date"
                                    name="custom_to"
                                    :value="request('custom_to')"
                                />
                            </x-admin::form.control-group>
                        </div>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.performance.filters.user') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="user_id"
                                :value="request('user_id')"
                                placeholder="{{ __('sales::app.performance.filters.all-users') }}"
                            >
                                <option value="">{{ __('sales::app.performance.filters.all-users') }}</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <div class="flex gap-[16px] items-center">
                            <button type="submit" class="primary-button">
                                {{ __('sales::app.performance.filters.apply') }}
                            </button>

                            <a href="{{ route('admin.sales.performance.index') }}" class="secondary-button">
                                {{ __('sales::app.performance.filters.clear') }}
                            </a>
                        </div>
                    </div>
                </form>
            </x-slot:form>
        </x-admin::layouts.filter>

        @if(isset($performanceData) && count($performanceData) > 0)
            <x-admin::table>
                <x-slot:head>
                    <x-admin::table.thead>
                        <x-admin::table.tr>
                            <x-admin::table.th>{{ __('sales::app.performance.table.sales-rep') }}</x-admin::table.th>
                            <x-admin::table.th>{{ __('sales::app.performance.table.period') }}</x-admin::table.th>
                            <x-admin::table.th>{{ __('sales::app.performance.table.target') }}</x-admin::table.th>
                            <x-admin::table.th>{{ __('sales::app.performance.table.actual') }}</x-admin::table.th>
                            <x-admin::table.th>{{ __('sales::app.performance.table.achievement') }}</x-admin::table.th>
                            <x-admin::table.th>{{ __('sales::app.performance.table.performance') }}</x-admin::table.th>
                            <x-admin::table.th>{{ __('sales::app.performance.table.actions') }}</x-admin::table.th>
                        </x-admin::table.tr>
                    </x-admin::table.thead>
                </x-slot:head>

                <x-slot:body>
                    @foreach($performanceData as $data)
                        <x-admin::table.tr>
                            <x-admin::table.td>
                                <div class="flex items-center gap-x-[10px]">
                                    <div class="w-[32px] h-[32px] text-[14px] font-bold bg-blue-500 text-white rounded-full flex items-center justify-center">
                                        {{ substr($data->user->name ?? 'N', 0, 1) }}
                                    </div>
                                    <span class="text-gray-600 dark:text-gray-300 font-semibold">{{ $data->user->name ?? 'N/A' }}</span>
                                </div>
                            </x-admin::table.td>
                            <x-admin::table.td>{{ ucfirst(str_replace('_', ' ', $data->period_type)) }} {{ $data->period_value }}, {{ $data->financial_year }}</x-admin::table.td>
                            <x-admin::table.td>{{ number_format($data->target_value, 2) }}</x-admin::table.td>
                            <x-admin::table.td>{{ number_format($data->achieved_value, 2) }}</x-admin::table.td>
                            <x-admin::table.td>
                                @php
                                    $percentage = $data->target_value > 0 ? (($data->achieved_value ?? 0) / $data->target_value) * 100 : 0;
                                @endphp
                                <div class="w-full h-[20px] bg-gray-200 rounded-full dark:bg-gray-700">
                                    <div class="h-[20px] rounded-full {{ $percentage >= 100 ? 'bg-green-600' : ($percentage >= 75 ? 'bg-yellow-500' : 'bg-red-600') }}" style="width: {{ min($percentage, 100) }}%;" aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </x-admin::table.td>
                            <x-admin::table.td>
                                @if($percentage >= 100)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Excellent</span>
                                @elseif($percentage >= 90)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Very Good</span>
                                @elseif($percentage >= 75)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Good</span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Needs Improvement</span>
                                @endif
                            </x-admin::table.td>
                            <x-admin::table.td>
                                <a href="{{ route('admin.sales.performance.user', $data->user_id) }}" class="transparent-button">
                                    <span class="icon-eye"></span>
                                </a>
                            </x-admin::table.td>
                        </x-admin::table.tr>
                    @endforeach
                </x-slot:body>
            </x-admin::table>
        @else
            <div class="flex flex-col justify-center items-center h-[calc(100vh-200px)]">
                <img src="{{ asset('vendor/webkul/admin/assets/images/empty-state.svg') }}" alt="Empty State" class="w-[100px] h-[100px] mb-[10px]">
                <p class="text-[16px] text-gray-400 font-semibold">No Performance Data</p>
                <p class="text-[14px] text-gray-400">No performance data available for the selected criteria.</p>
                <a href="{{ route('admin.sales.targets.index') }}" class="primary-button mt-[10px]">
                    Set Up Targets
                </a>
            </div>
        @endif
    </div>
</x-admin::layouts>
