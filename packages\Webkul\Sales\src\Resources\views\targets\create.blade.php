<x-admin::layouts>
    <x-slot:title>
        @lang('sales::app.targets.create.title')
    </x-slot>

    <!-- Create Sales Target Form -->
    <x-admin::form :action="route('admin.sales.targets.store')">
        <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
                <div class="flex flex-col gap-2">
                    <div class="flex cursor-pointer items-center">
                        <x-admin::breadcrumbs name="sales.targets.create" />
                    </div>

                    <div class="text-xl font-bold dark:text-white">
                        @lang('sales::app.targets.create.title')
                    </div>
                </div>

                <div class="flex items-center gap-x-2.5">
                    <div class="flex items-center gap-x-2.5">
                        <button
                            type="submit"
                            class="primary-button"
                        >
                            Save Target
                        </button>
                    </div>
                </div>
            </div>

            <div class="flex gap-2.5 max-xl:flex-wrap">
                <div class="flex flex-1 flex-col gap-2 max-xl:flex-auto">
                    <div class="box-shadow rounded bg-white p-4 dark:bg-gray-900">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.create.user')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="user_id"
                                :value="old('user_id')"
                                rules="required"
                                :label="trans('sales::app.targets.create.user')"
                                :placeholder="trans('sales::app.targets.create.select-user')"
                            >
                                <option value="">@lang('sales::app.targets.create.select-user')</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="user_id" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.create.financial-year')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="financial_year"
                                :value="old('financial_year')"
                                rules="required"
                                :label="trans('sales::app.targets.create.financial-year')"
                                :placeholder="trans('sales::app.targets.create.select-financial-year')"
                            >
                                <option value="">@lang('sales::app.targets.create.select-financial-year')</option>
                                @foreach($financialYears as $year)
                                    <option value="{{ $year }}" {{ old('financial_year') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endforeach
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="financial_year" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.create.period-type')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="period_type"
                                :value="old('period_type')"
                                rules="required"
                                :label="trans('sales::app.targets.create.period-type')"
                                :placeholder="trans('sales::app.targets.create.select-period-type')"
                            >
                                <option value="">@lang('sales::app.targets.create.select-period-type')</option>
                                <option value="annual" {{ old('period_type') == 'annual' ? 'selected' : '' }}>@lang('sales::app.targets.create.annual')</option>
                                <option value="half_yearly" {{ old('period_type') == 'half_yearly' ? 'selected' : '' }}>@lang('sales::app.targets.create.half-yearly')</option>
                                <option value="quarterly" {{ old('period_type') == 'quarterly' ? 'selected' : '' }}>@lang('sales::app.targets.create.quarterly')</option>
                                <option value="monthly" {{ old('period_type') == 'monthly' ? 'selected' : '' }}>@lang('sales::app.targets.create.monthly')</option>
                                <option value="custom" {{ old('period_type') == 'custom' ? 'selected' : '' }}>@lang('sales::app.targets.create.custom')</option>
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="period_type" />
                        </x-admin::form.control-group>

                        <div id="period_value_container">
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('sales::app.targets.create.period-value')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="select"
                                    name="period_value[]"
                                    :value="old('period_value')"
                                    :label="trans('sales::app.targets.create.period-value')"
                                    :placeholder="trans('sales::app.targets.create.select-period')"
                                    multiple
                                >
                                    <option value="">@lang('sales::app.targets.create.select-period')</option>
                                </x-admin::form.control-group.control>

                                <x-admin::form.control-group.error control-name="period_value" />
                            </x-admin::form.control-group>
                        </div>

                        <div id="custom_date_range_container" style="display: none;">
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('sales::app.targets.create.start-date')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="date"
                                    name="start_date"
                                    :value="old('start_date')"
                                    :label="trans('sales::app.targets.create.start-date')"
                                />

                                <x-admin::form.control-group.error control-name="start_date" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('sales::app.targets.create.end-date')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="date"
                                    name="end_date"
                                    :value="old('end_date')"
                                    :label="trans('sales::app.targets.create.end-date')"
                                />

                                <x-admin::form.control-group.error control-name="end_date" />
                            </x-admin::form.control-group>
                        </div>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.create.target-value')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="text"
                                name="target_value"
                                :value="old('target_value')"
                                rules="required|numeric"
                                :label="trans('sales::app.targets.create.target-value')"
                            />

                            <x-admin::form.control-group.error control-name="target_value" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.create.target-type')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="target_type"
                                :value="old('target_type')"
                                rules="required"
                                :label="trans('sales::app.targets.create.target-type')"
                            >
                                <option value="">@lang('sales::app.targets.create.select-target-type')</option>
                                <option value="revenue" {{ old('target_type') == 'revenue' ? 'selected' : '' }}>@lang('sales::app.targets.create.revenue')</option>
                                <option value="deals" {{ old('target_type') == 'deals' ? 'selected' : '' }}>@lang('sales::app.targets.create.deals')</option>
                                <option value="leads" {{ old('target_type') == 'leads' ? 'selected' : '' }}>@lang('sales::app.targets.create.leads')</option>
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="target_type" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                @lang('sales::app.targets.create.description')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="textarea"
                                name="description"
                                :value="old('description')"
                                :label="trans('sales::app.targets.create.description')"
                            />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.control
                                type="checkbox"
                                name="is_active"
                                :value="1"
                                :checked="old('is_active', true)"
                                :label="trans('sales::app.targets.create.is-active')"
                            />
                        </x-admin::form.control-group>
                    </div>
                </div>
            </div>
        </div>
    </x-admin::form>
</x-admin::layouts>

