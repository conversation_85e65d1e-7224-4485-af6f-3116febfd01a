<header class="sticky top-0 z-[10001] flex items-center justify-between border-b border-gray-200 bg-white px-4 py-2.5 transition-all dark:border-gray-800 dark:bg-gray-900">
    <!-- logo -->
    <div class="flex items-center gap-1.5">
        <i class="icon-menu hidden cursor-pointer rounded-md p-1.5 text-2xl hover:bg-gray-100 dark:hover:bg-gray-950 max-lg:block"></i>

        <a href="<?php echo e(route('admin.dashboard.index')); ?>">
            <img
                class="h-10"
                src="<?php echo e(request()->cookie('dark_mode') ? vite()->asset('images/dark-logo.svg') : vite()->asset('images/logo.svg')); ?>"
                id="logo-image"
                alt="<?php echo e(config('app.name')); ?>"
            />
        </a>
    </div>

    <div class="flex items-center gap-1.5">
        <!-- Mega Search Bar Vue Component -->
        <v-mega-search>
            <div class="relative flex w-[525px] max-w-[525px] items-center max-lg:w-[400px] ltr:ml-2.5 rtl:mr-2.5">
                <i class="icon-search absolute top-1.5 flex items-center text-2xl ltr:left-3 rtl:right-3"></i>

                <input
                    type="text"
                    class="block w-full rounded-3xl border bg-white px-10 py-1.5 leading-6 text-gray-600 transition-all hover:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300"
                    placeholder="<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.title'); ?>"
                >
            </div>
        </v-mega-search>

        <!-- Quick create section -->
        <div>
            <?php if(bouncer()->hasPermission('leads.create')
                || bouncer()->hasPermission('quotes.create')
                || bouncer()->hasPermission('mail.create')
                || bouncer()->hasPermission('contacts.persons.create')
                || bouncer()->hasPermission('contacts.organizations.create')
                || bouncer()->hasPermission('products.create')
                || bouncer()->hasPermission('settings.automation.attributes.create')
                || bouncer()->hasPermission('settings.user.roles.create')
                || bouncer()->hasPermission('settings.user.users.create')
            ): ?>
                <?php if (isset($component)) { $__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.dropdown.index','data' => ['position' => 'bottom-right']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['position' => 'bottom-right']); ?>
                     <?php $__env->slot('toggle', null, []); ?> 
                        <!-- Toggle Button -->
                        <button class="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-brandColor text-white">
                            <i class="icon-add text-2xl"></i>
                        </button>
                     <?php $__env->endSlot(); ?>

                    <!-- Dropdown Content -->
                     <?php $__env->slot('content', null, ['class' => 'mt-2 !p-0']); ?> 
                        <div class="relative px-2 py-4">
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <!-- Link to create new Lead -->
                                <?php if(bouncer()->hasPermission('leads.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.leads.create')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-leads text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.lead'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to create new Quotes -->
                                <?php if(bouncer()->hasPermission('quotes.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.quotes.create')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-quote text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.quote'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to send new Mail-->
                                <?php if(bouncer()->hasPermission('mail.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.mail.index', ['route' => 'inbox'])); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-mail text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.email'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to create new Person-->
                                <?php if(bouncer()->hasPermission('contacts.persons.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.contacts.persons.create')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-settings-user text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.person'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to create new Organizations -->
                                <?php if(bouncer()->hasPermission('contacts.organizations.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.contacts.organizations.create')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-organization text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.organization'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to create new Products -->
                                <?php if(bouncer()->hasPermission('products.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.products.create')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-product text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.product'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to create new Attributes -->
                                <?php if(bouncer()->hasPermission('settings.automation.attributes.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.settings.attributes.create')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-attribute text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.attribute'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to create new Role -->
                                <?php if(bouncer()->hasPermission('settings.user.roles.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.settings.roles.create')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-role text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.role'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <!-- Link to create new User-->
                                <?php if(bouncer()->hasPermission('settings.user.users.create')): ?>
                                    <div class="rounded-lg bg-white p-2 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-950">
                                        <a href="<?php echo e(route('admin.settings.users.index')); ?>">
                                            <div class="flex flex-col gap-1">
                                                <i class="icon-user text-2xl text-gray-600"></i>

                                                <span class="font-medium dark:text-gray-300"><?php echo app('translator')->get('admin::app.layouts.user'); ?></span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                     <?php $__env->endSlot(); ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2)): ?>
<?php $attributes = $__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2; ?>
<?php unset($__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2)): ?>
<?php $component = $__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2; ?>
<?php unset($__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2); ?>
<?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <div class="flex items-center gap-2.5">
        <!-- Dark mode -->
        <v-dark>
            <div class="flex">
                <span
                    class="<?php echo e(request()->cookie('dark_mode') ? 'icon-light' : 'icon-dark'); ?> p-1.5 rounded-md text-2xl cursor-pointer transition-all hover:bg-gray-100 dark:hover:bg-gray-950"
                ></span>
            </div>
        </v-dark>

        <!-- Admin profile -->
        <?php if (isset($component)) { $__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.dropdown.index','data' => ['position' => 'bottom-right']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['position' => 'bottom-right']); ?>
             <?php $__env->slot('toggle', null, []); ?> 
                <?php if(auth()->guard('user')->user()->image): ?>
                    <button class="flex h-9 w-9 cursor-pointer overflow-hidden rounded-full hover:opacity-80 focus:opacity-80">
                        <img
                            src="<?php echo e(auth()->guard('user')->user()->image_url); ?>"
                            class="w-full"
                        />
                    </button>
                <?php else: ?>
                    <button class="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-pink-400 font-semibold leading-6 text-white">
                        <?php echo e(substr(auth()->guard('user')->user()->name, 0, 1)); ?>

                    </button>
                <?php endif; ?>
             <?php $__env->endSlot(); ?>

            <!-- Admin Dropdown -->
             <?php $__env->slot('content', null, ['class' => '!p-0']); ?> 
                <div class="flex items-center gap-1.5 border border-b-gray-300 px-5 py-2.5 dark:border-gray-800">
                    <img
                        src="<?php echo e(url('cache/logo.png')); ?>"
                        width="24"
                        height="24"
                    />

                    <!-- Version -->
                    <p class="text-gray-400">
                        Version: v<?php echo e(core()->version()); ?>

                    </p>
                </div>

                <div class="grid gap-1 pb-2.5">
                    <a
                        class="cursor-pointer px-5 py-2 text-base text-gray-800 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-950"
                        href="<?php echo e(route('admin.user.account.edit')); ?>"
                    >
                        My Account
                    </a>

                    <!--Admin logout-->
                    <?php if (isset($component)) { $__componentOriginal81b4d293d9113446bb908fc8aef5c8f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.form.index','data' => ['method' => 'DELETE','action' => ''.e(route('admin.session.destroy')).'','id' => 'adminLogout']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['method' => 'DELETE','action' => ''.e(route('admin.session.destroy')).'','id' => 'adminLogout']); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6)): ?>
<?php $attributes = $__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6; ?>
<?php unset($__attributesOriginal81b4d293d9113446bb908fc8aef5c8f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal81b4d293d9113446bb908fc8aef5c8f6)): ?>
<?php $component = $__componentOriginal81b4d293d9113446bb908fc8aef5c8f6; ?>
<?php unset($__componentOriginal81b4d293d9113446bb908fc8aef5c8f6); ?>
<?php endif; ?>

                    <a
                        class="cursor-pointer px-5 py-2 text-base text-gray-800 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-950"
                        href="<?php echo e(route('admin.session.destroy')); ?>"
                        onclick="event.preventDefault(); document.getElementById('adminLogout').submit();"
                    >
                        Logout
                    </a>
                </div>
             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2)): ?>
<?php $attributes = $__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2; ?>
<?php unset($__attributesOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2)): ?>
<?php $component = $__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2; ?>
<?php unset($__componentOriginalaf937e0ec72fa678d3a0c6dc6c0ac5f2); ?>
<?php endif; ?>
    </div>
</header>

<?php if (! $__env->hasRenderedOnce('f0227a98-5467-4012-9c8b-befb42f168ce')): $__env->markAsRenderedOnce('f0227a98-5467-4012-9c8b-befb42f168ce');
$__env->startPush('scripts'); ?>
    <script
        type="text/x-template"
        id="v-mega-search-template"
    >
        <div class="relative flex w-[525px] max-w-[525px] items-center max-lg:w-[400px] ltr:ml-2.5 rtl:mr-2.5">
            <i class="icon-search absolute top-1.5 flex items-center text-2xl ltr:left-3 rtl:right-3"></i>

            <input
                type="text"
                class="peer block w-full rounded-3xl border bg-white px-10 py-1.5 leading-6 text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400"
                :class="{'border-gray-400': isDropdownOpen}"
                placeholder="<?php echo app('translator')->get('Search'); ?>"
                v-model.lazy="searchTerm"
                @click="searchTerm.length >= 2 ? isDropdownOpen = true : {}"
                v-debounce="500"
            >

            <div
                class="absolute top-10 z-10 w-full rounded-lg border bg-white shadow-[0px_0px_0px_0px_rgba(0,0,0,0.10),0px_1px_3px_0px_rgba(0,0,0,0.10),0px_5px_5px_0px_rgba(0,0,0,0.09),0px_12px_7px_0px_rgba(0,0,0,0.05),0px_22px_9px_0px_rgba(0,0,0,0.01),0px_34px_9px_0px_rgba(0,0,0,0.00)] dark:border-gray-800 dark:bg-gray-900"
                v-if="isDropdownOpen"
            >
                <!-- Search Tabs -->
                <div class="flex border-b text-sm text-gray-600 dark:border-gray-800 dark:text-gray-300">
                    <div
                        class="cursor-pointer p-4 hover:bg-gray-100 dark:hover:bg-gray-950"
                        :class="{ 'border-b-2 border-brandColor': activeTab == tab.key }"
                        v-for="tab in tabs"
                        @click="activeTab = tab.key; search();"
                    >
                        {{ tab.title }}
                    </div>
                </div>

                <!-- Searched Results -->
                <template v-if="activeTab == 'products'">
                    <template v-if="isLoading">
                        <?php if (isset($component)) { $__componentOriginaleba82ad7959d0b76a0ef438c765d88d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleba82ad7959d0b76a0ef438c765d88d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.header.mega-search.products','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.header.mega-search.products'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleba82ad7959d0b76a0ef438c765d88d7)): ?>
<?php $attributes = $__attributesOriginaleba82ad7959d0b76a0ef438c765d88d7; ?>
<?php unset($__attributesOriginaleba82ad7959d0b76a0ef438c765d88d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleba82ad7959d0b76a0ef438c765d88d7)): ?>
<?php $component = $__componentOriginaleba82ad7959d0b76a0ef438c765d88d7; ?>
<?php unset($__componentOriginaleba82ad7959d0b76a0ef438c765d88d7); ?>
<?php endif; ?>
                    </template>

                    <template v-else>
                        <div class="grid max-h-[400px] overflow-y-auto">
                            <template v-for="product in searchedResults.products">
                                <a
                                    :href="'<?php echo e(route('admin.products.view', ':id')); ?>'.replace(':id', product.id)"
                                    class="flex cursor-pointer justify-between gap-2.5 border-b border-slate-300 p-4 last:border-b-0 hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-950"
                                >
                                    <!-- Left Information -->
                                    <div class="flex gap-2.5">
                                        <!-- Details -->
                                        <div class="grid place-content-start gap-1.5">
                                            <p class="text-base font-semibold text-gray-600 dark:text-gray-300">
                                                {{ product.name }}
                                            </p>

                                            <p class="text-gray-500">
                                                {{ "<?php echo app('translator')->get(':sku'); ?>".replace(':sku', product.sku) }}
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Right Information -->
                                    <div class="grid place-content-center gap-1 text-right">
                                        <!-- Formatted Price -->
                                        <p class="font-semibold text-gray-600 dark:text-gray-300">
                                            {{ $admin.formatPrice(product.price) }}
                                        </p>
                                    </div>
                                </a>
                            </template>

                        </div>

                        <div class="flex border-t p-3 dark:border-gray-800">
                            <template v-if="searchedResults.products.length">
                                <a
                                    :href="'<?php echo e(route('admin.products.index')); ?>?search=:query'.replace(':query', searchTerm)"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >

                                    {{ `<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-matching-products'); ?>`.replace(':query', searchTerm).replace(':count', searchedResults.products.length) }}
                                </a>
                            </template>

                            <template v-else>
                                <a
                                    href="<?php echo e(route('admin.products.index')); ?>"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >
                                    <?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-products'); ?>
                                </a>
                            </template>
                        </div>
                    </template>
                </template>

                <template v-if="activeTab == 'leads'">
                    <template v-if="isLoading">
                        <?php if (isset($component)) { $__componentOriginal2f925dcb08db4e6b27de8bea570f7017 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2f925dcb08db4e6b27de8bea570f7017 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.header.mega-search.leads','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.header.mega-search.leads'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2f925dcb08db4e6b27de8bea570f7017)): ?>
<?php $attributes = $__attributesOriginal2f925dcb08db4e6b27de8bea570f7017; ?>
<?php unset($__attributesOriginal2f925dcb08db4e6b27de8bea570f7017); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2f925dcb08db4e6b27de8bea570f7017)): ?>
<?php $component = $__componentOriginal2f925dcb08db4e6b27de8bea570f7017; ?>
<?php unset($__componentOriginal2f925dcb08db4e6b27de8bea570f7017); ?>
<?php endif; ?>
                    </template>

                    <template v-else>
                        <div class="grid max-h-[400px] overflow-y-auto">
                            <template v-for="lead in searchedResults.leads">
                                <a
                                    :href="'<?php echo e(route('admin.leads.view', ':id')); ?>'.replace(':id', lead.id)"
                                    class="flex cursor-pointer justify-between gap-2.5 border-b border-slate-300 p-4 last:border-b-0 hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-950"
                                >
                                    <!-- Left Information -->
                                    <div class="flex gap-2.5">
                                        <!-- Details -->
                                        <div class="grid place-content-start gap-1.5">
                                            <p class="text-base font-semibold text-gray-600 dark:text-gray-300">
                                                {{ lead.title }}
                                            </p>

                                            <p class="text-gray-500">
                                                {{ lead.description }}
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Right Information -->
                                    <div class="grid place-content-center gap-1 text-right">
                                        <!-- Formatted Price -->
                                        <p class="font-semibold text-gray-600 dark:text-gray-300">
                                            {{ $admin.formatPrice(lead.lead_value) }}
                                        </p>
                                    </div>
                                </a>
                            </template>
                        </div>

                        <div class="flex border-t p-3 dark:border-gray-800">
                            <template v-if="searchedResults.leads.length">
                                <a
                                    :href="'<?php echo e(route('admin.leads.index')); ?>?search=:query'.replace(':query', searchTerm)"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >
                                    {{ `<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-matching-leads'); ?>`.replace(':query', searchTerm).replace(':count', searchedResults.leads.length) }}
                                </a>
                            </template>

                            <template v-else>
                                <a
                                    href="<?php echo e(route('admin.leads.index')); ?>"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >
                                    <?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-leads'); ?>
                                </a>
                            </template>
                        </div>
                    </template>
                </template>

                <template v-if="activeTab == 'persons'">
                    <template v-if="isLoading">
                        <?php if (isset($component)) { $__componentOriginal30684fc1b7874708f9dfcb75a1b97bcd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal30684fc1b7874708f9dfcb75a1b97bcd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.header.mega-search.persons','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.header.mega-search.persons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal30684fc1b7874708f9dfcb75a1b97bcd)): ?>
<?php $attributes = $__attributesOriginal30684fc1b7874708f9dfcb75a1b97bcd; ?>
<?php unset($__attributesOriginal30684fc1b7874708f9dfcb75a1b97bcd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal30684fc1b7874708f9dfcb75a1b97bcd)): ?>
<?php $component = $__componentOriginal30684fc1b7874708f9dfcb75a1b97bcd; ?>
<?php unset($__componentOriginal30684fc1b7874708f9dfcb75a1b97bcd); ?>
<?php endif; ?>
                    </template>

                    <template v-else>
                        <div class="grid max-h-[400px] overflow-y-auto">
                            <template v-for="person in searchedResults.persons">
                                <a
                                    :href="'<?php echo e(route('admin.contacts.persons.view', ':id')); ?>'.replace(':id', person.id)"
                                    class="flex cursor-pointer justify-between gap-2.5 border-b border-slate-300 p-4 last:border-b-0 hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-950"
                                >
                                    <!-- Left Information -->
                                    <div class="flex gap-2.5">
                                        <!-- Details -->
                                        <div class="grid place-content-start gap-1.5">
                                            <p class="text-base font-semibold text-gray-600 dark:text-gray-300">
                                                {{ person.name }}
                                            </p>

                                            <p class="text-gray-500">
                                                {{ person.emails.map((item) => `${item.value}(${item.label})`).join(', ') }}
                                            </p>
                                        </div>
                                    </div>
                                </a>
                            </template>
                        </div>

                        <div class="flex border-t p-3 dark:border-gray-800">
                            <template v-if="searchedResults.persons.length">
                                <a
                                    :href="'<?php echo e(route('admin.contacts.persons.index')); ?>?search=:query'.replace(':query', searchTerm)"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >
                                    {{ `<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-matching-contacts'); ?>`.replace(':query', searchTerm).replace(':count', searchedResults.persons.length) }}
                                </a>
                            </template>

                            <template v-else>
                                <a
                                    href="<?php echo e(route('admin.contacts.persons.index')); ?>"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >
                                    <?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-contacts'); ?>
                                </a>
                            </template>
                        </div>
                    </template>
                </template>

                <template v-if="activeTab == 'quotes'">
                    <template v-if="isLoading">
                        <?php if (isset($component)) { $__componentOriginal6e06320b6437488bf17b056981c9f8ad = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6e06320b6437488bf17b056981c9f8ad = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.header.mega-search.quotes','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.header.mega-search.quotes'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6e06320b6437488bf17b056981c9f8ad)): ?>
<?php $attributes = $__attributesOriginal6e06320b6437488bf17b056981c9f8ad; ?>
<?php unset($__attributesOriginal6e06320b6437488bf17b056981c9f8ad); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6e06320b6437488bf17b056981c9f8ad)): ?>
<?php $component = $__componentOriginal6e06320b6437488bf17b056981c9f8ad; ?>
<?php unset($__componentOriginal6e06320b6437488bf17b056981c9f8ad); ?>
<?php endif; ?>
                    </template>

                    <template v-else>
                        <div class="grid max-h-[400px] overflow-y-auto">
                            <template v-for="quote in searchedResults.quotes">
                                <a
                                    :href="'<?php echo e(route('admin.quotes.edit', ':id')); ?>'.replace(':id', quote.id)"
                                    class="flex cursor-pointer justify-between gap-2.5 border-b border-slate-300 p-4 last:border-b-0 hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-950"
                                >
                                    <!-- Left Information -->
                                    <div class="flex gap-2.5">
                                        <!-- Details -->
                                        <div class="grid place-content-start gap-1.5">
                                            <p class="text-base font-semibold text-gray-600 dark:text-gray-300">
                                                {{ quote.subject }}
                                            </p>

                                            <p class="text-gray-500">
                                                {{ quote.description }}
                                            </p>
                                        </div>
                                    </div>
                                </a>
                            </template>
                        </div>

                        <div class="flex border-t p-3 dark:border-gray-800">
                            <template v-if="searchedResults.quotes.length">
                                <a
                                    :href="'<?php echo e(route('admin.quotes.index')); ?>?search=:query'.replace(':query', searchTerm)"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >
                                    {{ `<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-matching-quotes'); ?>`.replace(':query', searchTerm).replace(':count', searchedResults.quotes.length) }}
                                </a>
                            </template>

                            <template v-else>
                                <a
                                    href="<?php echo e(route('admin.quotes.index')); ?>"
                                    class="cursor-pointer text-xs font-semibold text-brandColor transition-all hover:underline"
                                >
                                    <?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.explore-all-quotes'); ?>
                                </a>
                            </template>
                        </div>
                    </template>
                </template>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-mega-search', {
            template: '#v-mega-search-template',

            data() {
                return  {
                    activeTab: 'leads',

                    isDropdownOpen: false,

                    tabs: {
                        leads: {
                            key: 'leads',
                            title: "<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.tabs.leads'); ?>",
                            is_active: true,
                            endpoint: "<?php echo e(route('admin.leads.search')); ?>",
                            query_params: [
                                {
                                    search: 'title',
                                    searchFields: 'title:like',
                                },
                                {
                                    search: 'user.name',
                                    searchFields: 'user.name:like',
                                },
                                {
                                    search: 'person.name',
                                    searchFields: 'person.name:like',
                                },
                            ],
                        },

                        quotes: {
                            key: 'quotes',
                            title: "<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.tabs.quotes'); ?>",
                            is_active: false,
                            endpoint: "<?php echo e(route('admin.quotes.search')); ?>",
                            query_params: [
                                {
                                    search: 'subject',
                                    searchFields: 'subject:like',
                                },
                                {
                                    search: 'description',
                                    searchFields: 'description:like',
                                },
                                {
                                    search: 'user.name',
                                    searchFields: 'user.name:like',
                                },
                                {
                                    search: 'person.name',
                                    searchFields: 'person.name:like',
                                },
                            ],
                        },

                        products: {
                            key: 'products',
                            title: "<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.tabs.products'); ?>",
                            is_active: false,
                            endpoint: "<?php echo e(route('admin.products.search')); ?>",
                            query_params: [
                                {
                                    search: 'name',
                                    searchFields: 'name:like',
                                },
                                {
                                    search: 'sku',
                                    searchFields: 'sku:like',
                                },
                                {
                                    search: 'description',
                                    searchFields: 'description:like',
                                },
                            ],
                        },

                        persons: {
                            key: 'persons',
                            title: "<?php echo app('translator')->get('admin::app.components.layouts.header.mega-search.tabs.persons'); ?>",
                            is_active: false,
                            endpoint: "<?php echo e(route('admin.contacts.persons.search')); ?>",
                            query_params: [
                                {
                                    search: 'name',
                                    searchFields: 'name:like',
                                },
                                {
                                    search: 'job_title',
                                    searchFields: 'job_title:like',
                                },
                                {
                                    search: 'user.name',
                                    searchFields: 'user.name:like',
                                },
                                {
                                    search: 'organization.name',
                                    searchFields: 'organization.name:like',
                                },
                            ],
                        },
                    },

                    isLoading: false,

                    searchTerm: '',

                    searchedResults: {
                        leads: [],
                        quotes: [],
                        products: [],
                        persons: []
                    },

                    params: {
                        search: '',
                        searchFields: '',
                    },
                };
            },

            watch: {
                searchTerm: 'updateSearchParams',

                activeTab: 'updateSearchParams',
            },

            created() {
                window.addEventListener('click', this.handleFocusOut);
            },

            beforeDestroy() {
                window.removeEventListener('click', this.handleFocusOut);
            },

            methods: {
                search(endpoint) {
                    if (this.searchTerm.length <= 1) {
                        this.searchedResults[this.activeTab] = [];

                        this.isDropdownOpen = false;

                        return;
                    }

                    this.isDropdownOpen = true;

                    this.$axios.get(endpoint, {
                            params: {
                                ...this.params,
                            },
                        })
                        .then((response) => {
                            this.searchedResults[this.activeTab] = response.data.data;
                        })
                        .catch((error) => {})
                        .finally(() => this.isLoading = false);
                },

                handleFocusOut(e) {
                    if (! this.$el.contains(e.target)) {
                        this.isDropdownOpen = false;
                    }
                },

                updateSearchParams() {
                    const newTerm = this.searchTerm;

                    this.params = {
                        search: '',
                        searchFields: '',
                    };

                    const tab = this.tabs[this.activeTab];

                    this.params.search += tab.query_params.map((param) => `${param.search}:${newTerm};`).join('');

                    this.params.searchFields += tab.query_params.map((param) => `${param.searchFields};`).join('');

                    this.search(tab.endpoint);
                },
            },
        });
    </script>

    <script
        type="text/x-template"
        id="v-dark-template"
    >
        <div class="flex">
            <span
                class="cursor-pointer rounded-md p-1.5 text-2xl transition-all hover:bg-gray-100 dark:hover:bg-gray-950"
                :class="[isDarkMode ? 'icon-light' : 'icon-dark']"
                @click="toggle"
            ></span>
        </div>
    </script>

    <script type="module">
        app.component('v-dark', {
            template: '#v-dark-template',

            data() {
                return {
                    isDarkMode: <?php echo e(request()->cookie('dark_mode') ?? 0); ?>,

                    logo: "<?php echo e(vite()->asset('images/logo.svg')); ?>",

                    dark_logo: "<?php echo e(vite()->asset('images/dark-logo.svg')); ?>",
                };
            },

            methods: {
                toggle() {
                    this.isDarkMode = parseInt(this.isDarkModeCookie()) ? 0 : 1;

                    var expiryDate = new Date();

                    expiryDate.setMonth(expiryDate.getMonth() + 1);

                    document.cookie = 'dark_mode=' + this.isDarkMode + '; path=/; expires=' + expiryDate.toGMTString();

                    document.documentElement.classList.toggle('dark', this.isDarkMode === 1);

                    if (this.isDarkMode) {
                        this.$emitter.emit('change-theme', 'dark');

                        document.getElementById('logo-image').src = this.dark_logo;
                    } else {
                        this.$emitter.emit('change-theme', 'light');

                        document.getElementById('logo-image').src = this.logo;
                    }
                },

                isDarkModeCookie() {
                    const cookies = document.cookie.split(';');

                    for (const cookie of cookies) {
                        const [name, value] = cookie.trim().split('=');

                        if (name === 'dark_mode') {
                            return value;
                        }
                    }

                    return 0;
                },
            },
        });
    </script>
<?php $__env->stopPush(); endif; ?>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/components/layouts/header/index.blade.php ENDPATH**/ ?>