<x-admin::layouts>
    <x-slot:title>
        @lang('sales::app.targets.title')
    </x-slot>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <x-admin::breadcrumbs name="sales.targets" />
                </div>

                <div class="text-xl font-bold dark:text-white">
                    @lang('sales::app.targets.title')
                </div>
            </div>

            <div class="flex items-center gap-x-2.5">
                <div class="flex items-center gap-x-2.5">
                    <a
                        href="{{ route('admin.sales.targets.create') }}"
                        class="primary-button"
                    >
                        @lang('sales::app.targets.create')
                    </a>
                </div>
            </div>
        </div>

        <x-admin::layouts.filter>
            <x-slot:form>
                <form method="GET" action="{{ route('admin.sales.targets.index') }}">
                    <div class="flex gap-[16px] items-center">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.targets.filters.financial-year') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="financial_year"
                                :value="(string) request('financial_year')"
                                placeholder="{{ __('sales::app.targets.filters.all-years') }}"
                            >
                                <option value="">{{ __('sales::app.targets.filters.all-years') }}</option>
                                @foreach($financialYears as $year)
                                    <option value="{{ $year }}" {{ request('financial_year') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endforeach
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.targets.filters.period-type') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="period_type"
                                :value="request('period_type')"
                                placeholder="{{ __('sales::app.targets.filters.all-periods') }}"
                            >
                                <option value="">{{ __('sales::app.targets.filters.all-periods') }}</option>
                                <option value="annual" {{ request('period_type') == 'annual' ? 'selected' : '' }}>{{ __('sales::app.targets.filters.annual') }}</option>
                                <option value="half_yearly" {{ request('period_type') == 'half_yearly' ? 'selected' : '' }}>{{ __('sales::app.targets.filters.half-yearly') }}</option>
                                <option value="quarterly" {{ request('period_type') == 'quarterly' ? 'selected' : '' }}>{{ __('sales::app.targets.filters.quarterly') }}</option>
                                <option value="monthly" {{ request('period_type') == 'monthly' ? 'selected' : '' }}>{{ __('sales::app.targets.filters.monthly') }}</option>
                                <option value="custom" {{ request('period_type') == 'custom' ? 'selected' : '' }}>{{ __('sales::app.targets.filters.custom') }}</option>
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <div id="custom_date_range" class="flex gap-[16px] items-center" style="display: none;">
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    {{ __('sales::app.targets.filters.custom-from') }}
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="date"
                                    name="custom_from"
                                    :value="request('custom_from')"
                                />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    {{ __('sales::app.targets.filters.custom-to') }}
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="date"
                                    name="custom_to"
                                    :value="request('custom_to')"
                                />
                            </x-admin::form.control-group>
                        </div>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                {{ __('sales::app.targets.filters.user') }}
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="user_id"
                                :value="request('user_id')"
                                placeholder="{{ __('sales::app.targets.filters.all-users') }}"
                            >
                                <option value="">{{ __('sales::app.targets.filters.all-users') }}</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </x-admin::form.control-group.control>
                        </x-admin::form.control-group>

                        <div class="flex gap-[16px] items-center">
                            <button type="submit" class="primary-button">
                                {{ __('sales::app.targets.filters.apply') }}
                            </button>

                            <a href="{{ route('admin.sales.targets.index') }}" class="secondary-button">
                                {{ __('sales::app.targets.filters.clear') }}
                            </a>
                        </div>
                    </div>
                </form>
            </x-slot:form>
        </x-admin::layouts.filter>

        <x-admin::table>
            <x-slot:head>
                <x-admin::table.thead>
                    <x-admin::table.tr>
                        <x-admin::table.th>{{ __('sales::app.targets.table.user') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.period') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.target-value') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.achieved-value') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.progress') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.status') }}</x-admin::table.th>
                        <x-admin::table.th>{{ __('sales::app.targets.table.actions') }}</x-admin::table.th>
                    </x-admin::table.tr>
                </x-admin::table.thead>
            </x-slot:head>

            <x-slot:body>
                @foreach ($targets as $target)
                    @php dd($target); @endphp
                    <x-admin::table.tr>
                        <x-admin::table.td>{{ $target->user->name }}</x-admin::table.td>
                                                <x-admin::table.td>
                    {{ ucfirst(str_replace('_', ' ', $target->period_type)) }}
                    @if ($target->period_type === 'custom')
                        {{ $target->start_date }} - {{ $target->end_date }}
                    @else
                        @php
                            $periodValue = is_array($target->period_value) ? $target->period_value : (is_string($target->period_value) ? [$target->period_value] : (array) $target->period_value);
                        @endphp
                        {{ implode(', ', $periodValue) }}
                    @endif
                </x-admin::table.td>
                        <x-admin::table.td>{{ number_format($target->target_value, 2) }}</x-admin::table.td>
                        <x-admin::table.td>{{ number_format($target->achieved_value, 2) }}</x-admin::table.td>
                        <x-admin::table.td>
                            @php
                                $percentage = $target->target_value > 0 ? (($target->achieved_value ?? 0) / $target->target_value) * 100 : 0;
                            @endphp
                            <div class="w-full h-[20px] bg-gray-200 rounded-full dark:bg-gray-700">
                                <div class="h-[20px] rounded-full {{ $percentage >= 100 ? 'bg-green-600' : ($percentage >= 75 ? 'bg-yellow-500' : 'bg-red-600') }}" style="width: {{ min($percentage, 100) }}%;" aria-valuenow="{{ $percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </x-admin::table.td>
                        <x-admin::table.td>
                            @if($percentage >= 100)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Target Met</span>
                            @elseif($percentage >= 75)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">On Track</span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Below Target</span>
                            @endif
                        </x-admin::table.td>
                        <x-admin::table.td>
                            <div class="flex gap-x-[10px] items-center">
                                <a href="{{ route('admin.sales.targets.edit', $target->id) }}" class="transparent-button">
                                    <span class="icon-edit"></span>
                                </a>
                                <form action="{{ route('admin.sales.targets.destroy', $target->id) }}" method="POST" onsubmit="return confirm('@lang('sales::app.targets.delete-confirm')')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="transparent-button text-red-600">
                                    <span class="icon-delete"></span>
                                </button>
                            </form>
                            </div>
                        </x-admin::table.td>
                    </x-admin::table.tr>
                @endforeach
            </x-slot:body>
        </x-admin::table>

        @if(method_exists($targets, 'links'))
            <div class="flex justify-end mt-[16px]">
                {{ $targets->links() }}
            </div>
        @endif
    </div>
</x-admin::layouts>

