<x-admin::layouts>
    <x-slot:title>
        @lang('sales::app.targets.title')
    </x-slot>

    <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <x-admin::breadcrumbs name="sales.targets" />
                </div>

                <div class="text-xl font-bold dark:text-white">
                    @lang('sales::app.targets.title')
                </div>
            </div>

            <div class="flex items-center gap-x-2.5">
                <div class="flex items-center gap-x-2.5">
                    <a
                        href="{{ route('admin.sales.targets.create') }}"
                        class="primary-button"
                    >
                        @lang('sales::app.targets.create-title')
                    </a>
                </div>
            </div>
        </div>

        <x-admin::table>
            <x-admin::table.thead>
                <x-admin::table.thead.tr>
                    <x-admin::table.th>User</x-admin::table.th>
                    <x-admin::table.th>Financial Year</x-admin::table.th>
                    <x-admin::table.th>Period</x-admin::table.th>
                    <x-admin::table.th>Target Type</x-admin::table.th>
                    <x-admin::table.th>Target Value</x-admin::table.th>
                    <x-admin::table.th>Status</x-admin::table.th>
                    <x-admin::table.th class="text-right">Action</x-admin::table.th>
                </x-admin::table.thead.tr>
            </x-admin::table.thead>

            <x-admin::table.tbody>
                @forelse ($targets as $target)
                    <x-admin::table.tbody.tr>
                        <x-admin::table.td>{{ $target->user->name }}</x-admin::table.td>
                        <x-admin::table.td>{{ $target->financial_year }}</x-admin::table.td>
                        <x-admin::table.td>{{ $target->period_type }}</x-admin::table.td>
                        <x-admin::table.td>{{ $target->target_type }}</x-admin::table.td>
                        <x-admin::table.td>{{ $target->target_value }}</x-admin::table.td>
                        <x-admin::table.td>
                            <span class="label-{{ $target->status == 'active' ? 'active' : 'inactive' }}">
                                {{ $target->status == 'active' ? __('sales::app.targets.index.active') : __('sales::app.targets.index.inactive') }}
                            </span>
                        </x-admin::table.td>
                        <x-admin::table.td class="text-right">
                            <a href="{{ route('admin.sales.targets.edit', $target->id) }}">
                                <span class="icon-edit text-2xl"></span>
                            </a>
                        </x-admin::table.td>
                    </x-admin::table.tbody.tr>
                @empty
                    <x-admin::table.tbody.tr>
                        <x-admin::table.td colspan="7" class="text-center">
                            @lang('sales::app.targets.index.no-targets-found')
                        </x-admin::table.td>
                    </x-admin::table.tbody.tr>
                @endforelse
            </x-admin::table.tbody>
        </x-admin::table>
    </div>
</x-admin::layouts>
