<x-admin::layouts>
    <x-slot:title>
        @lang('sales::app.targets.edit.title')
    </x-slot>

    <!-- Edit Sales Target Form -->
    <x-admin::form :action="route('admin.sales.targets.update', $target->id)" method="PUT">
        <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
                <div class="flex flex-col gap-2">
                    <div class="flex cursor-pointer items-center">
                        <x-admin::breadcrumbs name="sales.targets.edit" :entity="$target" />
                    </div>

                    <div class="text-xl font-bold dark:text-white">
                        @lang('sales::app.targets.edit.title')
                    </div>
                </div>

                <div class="flex items-center gap-x-2.5">
                    <div class="flex items-center gap-x-2.5">
                        <button
                            type="submit"
                            class="primary-button"
                        >
                            @lang('sales::app.common.save')
                        </button>
                    </div>
                </div>
            </div>

            <div class="rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
                <div class="flex flex-col gap-4 p-4">
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.user')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="user_id"
                                :value="old('user_id', $target->user_id)"
                                rules="required"
                                :label="trans('sales::app.targets.user')"
                            >
                                <option value="">@lang('sales::app.targets.create.select-user')</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id', $target->user_id) == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="user_id" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.financial-year')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="text"
                                name="financial_year"
                                :value="old('financial_year', $target->financial_year)"
                                rules="required"
                                :label="trans('sales::app.targets.financial-year')"
                                :placeholder="trans('sales::app.targets.financial-year')"
                            />

                            <x-admin::form.control-group.error control-name="financial_year" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.period-type')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="period_type"
                                :value="old('period_type', $target->period_type)"
                                rules="required"
                                :label="trans('sales::app.targets.period-type')"
                            >
                                <option value="">@lang('sales::app.targets.create.select-period-type')</option>
                                <option value="annual" {{ old('period_type', $target->period_type) == 'annual' ? 'selected' : '' }}>@lang('sales::app.targets.period-types.annual')</option>
                                <option value="quarterly" {{ old('period_type', $target->period_type) == 'quarterly' ? 'selected' : '' }}>@lang('sales::app.targets.period-types.quarterly')</option>
                                <option value="monthly" {{ old('period_type', $target->period_type) == 'monthly' ? 'selected' : '' }}>@lang('sales::app.targets.period-types.monthly')</option>
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="period_type" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.period-value')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="period_value[]"
                                :value="old('period_value', $target->period_value)"
                                :label="trans('sales::app.targets.period-value')"
                                :placeholder="trans('sales::app.targets.select-period')"
                                multiple
                            >
                                <option value="">@lang('sales::app.targets.select-period')</option>
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="period_value" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.target-type')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="target_type"
                                :value="old('target_type', $target->target_type)"
                                rules="required"
                                :label="trans('sales::app.targets.target-type')"
                            >
                                <option value="">@lang('sales::app.targets.create.select-target-type')</option>
                                <option value="revenue" {{ old('target_type', $target->target_type) == 'revenue' ? 'selected' : '' }}>@lang('sales::app.targets.create.revenue')</option>
                                <option value="deals" {{ old('target_type', $target->target_type) == 'deals' ? 'selected' : '' }}>@lang('sales::app.targets.create.deals')</option>
                                <option value="leads" {{ old('target_type', $target->target_type) == 'leads' ? 'selected' : '' }}>@lang('sales::app.targets.create.leads')</option>
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="target_type" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label class="required">
                                @lang('sales::app.targets.target-value')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="number"
                                name="target_value"
                                :value="old('target_value', $target->target_value)"
                                rules="required|min:0"
                                step="0.01"
                                :label="trans('sales::app.targets.target-value')"
                                :placeholder="trans('sales::app.targets.target-value')"
                            />

                            <x-admin::form.control-group.error control-name="target_value" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                @lang('sales::app.targets.achieved-value')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="number"
                                name="achieved_value"
                                :value="old('achieved_value', $target->achieved_value)"
                                step="0.01"
                                :label="trans('sales::app.targets.achieved-value')"
                                :placeholder="trans('sales::app.targets.achieved-value')"
                            />

                            <x-admin::form.control-group.error control-name="achieved_value" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                @lang('sales::app.targets.start-date')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="date"
                                name="start_date"
                                :value="old('start_date', $target->start_date?->format('Y-m-d'))"
                                :label="trans('sales::app.targets.start-date')"
                            />

                            <x-admin::form.control-group.error control-name="start_date" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                @lang('sales::app.targets.end-date')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="date"
                                name="end_date"
                                :value="old('end_date', $target->end_date?->format('Y-m-d'))"
                                :label="trans('sales::app.targets.end-date')"
                            />

                            <x-admin::form.control-group.error control-name="end_date" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group>
                            <x-admin::form.control-group.label>
                                @lang('sales::app.targets.status')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="select"
                                name="status"
                                :value="old('status', $target->status)"
                                :label="trans('sales::app.targets.status')"
                            >
                                <option value="active" {{ old('status', $target->status) == 'active' ? 'selected' : '' }}>@lang('sales::app.targets.statuses.active')</option>
                                <option value="inactive" {{ old('status', $target->status) == 'inactive' ? 'selected' : '' }}>@lang('sales::app.targets.statuses.inactive')</option>
                                <option value="completed" {{ old('status', $target->status) == 'completed' ? 'selected' : '' }}>@lang('sales::app.targets.statuses.completed')</option>
                            </x-admin::form.control-group.control>

                            <x-admin::form.control-group.error control-name="status" />
                        </x-admin::form.control-group>

                        <x-admin::form.control-group class="md:col-span-2">
                            <x-admin::form.control-group.label>
                                @lang('sales::app.targets.notes')
                            </x-admin::form.control-group.label>

                            <x-admin::form.control-group.control
                                type="textarea"
                                name="notes"
                                :value="old('notes', $target->notes)"
                                :label="trans('sales::app.targets.notes')"
                            />
                        </x-admin::form.control-group>
                    </div>
                </div>
            </div>
        </div>
    </x-admin::form>
</x-admin::layouts>
