<template>
    <div class="box-shadow flex flex-col gap-4 rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900 max-xl:flex-wrap">
        <div class="flex gap-2 border-b border-gray-200 dark:border-gray-800">
            <template v-for="tab in tabs" :key="tab.id">
                <a
                    :href="'#' + tab.id"
                    :class="[
                        'inline-block px-4 py-3 text-sm font-medium',
                        activeTab === tab.id ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    ]"
                    @click.prevent="activeTab = tab.id"
                >
                    {{ tab.name }}
                </a>
            </template>
        </div>

        <div class="p-4">
            <form @submit.prevent="submitForm">
                <div v-show="activeTab === 'details'">
                    <!-- Form fields for lead details -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="required">First Name</label>
                            <input type="text" v-model="form.first_name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>Last Name</label>
                            <input type="text" v-model="form.last_name" class="form-control">
                        </div>
                    </div>
                    <!-- More form fields... -->
                </div>

                <div v-show="activeTab === 'notes'">
                    <!-- Form fields for notes -->
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea v-model="form.notes" class="form-control" rows="4"></textarea>
                    </div>
                </div>

                <div class="flex justify-end gap-2 mt-4">
                    <button type="button" class="btn btn-secondary" @click="cancel">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            activeTab: 'details',
            tabs: [
                { id: 'details', name: 'Details' },
                { id: 'notes', name: 'Notes' }
            ],
            form: {
                first_name: '',
                last_name: '',
                notes: ''
            }
        }
    },
    methods: {
        submitForm() {
            // Handle form submission
            console.log('Form submitted', this.form);
        },
        cancel() {
            // Handle cancel action
            console.log('Form cancelled');
        }
    }
}
</script>
