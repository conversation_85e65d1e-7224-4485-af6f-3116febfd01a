<?php

namespace Webkul\Sales\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Webkul\Sales\Models\SalesAchievement;
use Webkul\Sales\Models\SalesTarget;

class SeedSampleData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sales:seed-sample-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed sample sales data for demonstration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Seeding sample sales data...');

        // Update some targets with achieved values
        $targets = SalesTarget::limit(10)->get();

        foreach ($targets as $target) {
            // Generate random achievement between 0% and 120%
            $achievementPercentage = rand(0, 120);
            $achievedValue = ($target->target_value * $achievementPercentage) / 100;

            $target->update([
                'achieved_value'         => $achievedValue,
                'achievement_percentage' => $achievementPercentage,
            ]);

            // Create some sample achievements
            if ($achievementPercentage > 0) {
                SalesAchievement::create([
                    'user_id'           => $target->user_id,
                    'sales_target_id'   => $target->id,
                    'achievement_date'  => Carbon::now()->subDays(rand(1, 30)),
                    'achievement_type'  => 'revenue',
                    'achievement_value' => $achievedValue,
                    'source_type'       => 'manual',
                    'source_id'         => null,
                    'description'       => 'Sample achievement for demonstration',
                ]);
            }

            $this->info("Updated target {$target->id} with {$achievementPercentage}% achievement");
        }

        $this->info('Sample data seeded successfully!');
    }
}
