<v-modal-confirm ref="confirmModal"></v-modal-confirm>

<?php if (! $__env->hasRenderedOnce('6947506c-9bec-4c4f-bfe1-6ff5178e78dc')): $__env->markAsRenderedOnce('6947506c-9bec-4c4f-bfe1-6ff5178e78dc');
$__env->startPush('scripts'); ?>
    <script
        type="text/x-template"
        id="v-modal-confirm-template"
    >
        <div>
            <transition
                tag="div"
                name="modal-overlay"
                enter-class="duration-300 ease-out"
                enter-from-class="opacity-0"
                enter-to-class="opacity-100"
                leave-class="duration-200 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
            >
                <div
                    class="fixed inset-0 z-[10003] bg-gray-500 bg-opacity-50 transition-opacity"
                    v-show="isOpen"
                ></div>
            </transition>

            <transition
                tag="div"
                name="modal-content"
                enter-class="duration-300 ease-out"
                enter-from-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
                enter-to-class="translate-y-0 opacity-100 md:scale-100"
                leave-class="duration-200 ease-in"
                leave-from-class="translate-y-0 opacity-100 md:scale-100"
                leave-to-class="translate-y-4 opacity-0 md:translate-y-0 md:scale-95"
            >
                <div
                    class="fixed inset-0 z-[10004] transform overflow-y-auto transition"
                    v-if="isOpen"
                >
                    <div class="flex min-h-full items-end justify-center p-5 sm:items-center sm:p-0">
                        <div class="box-shadow absolute left-1/2 top-1/2 z-[999] w-full max-w-[400px] -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white dark:bg-gray-900 max-md:w-[90%]">
                            <div class="flex items-center justify-between gap-2.5 border-b px-4 py-3 text-lg font-bold text-gray-800 dark:border-gray-800 dark:text-white">
                                {{ title }}
                            </div>

                            <div class="px-4 py-3 text-left text-gray-600 dark:text-gray-300">
                                {{ message }}
                            </div>

                            <div class="flex justify-end gap-2.5 px-4 py-2.5">
                                <button type="button" class="transparent-button" @click="disagree">
                                    {{ options.btnDisagree }}
                                </button>

                                <button type="button" class="primary-button" @click="agree">
                                    {{ options.btnAgree }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </script>

    <script type="module">
        app.component('v-modal-confirm', {
            template: '#v-modal-confirm-template',

            data() {
                return {
                    isOpen: false,

                    title: '',

                    message: '',

                    options: {
                        btnDisagree: '',
                        btnAgree: '',
                    },

                    agreeCallback: null,

                    disagreeCallback: null,
                };
            },

            created() {
                this.registerGlobalEvents();
            },

            methods: {
                open({
                    title = "<?php echo app('translator')->get('admin::app.components.modal.confirm.title'); ?>",
                    message = "<?php echo app('translator')->get('admin::app.components.modal.confirm.message'); ?>",
                    options = {
                        btnDisagree: "<?php echo app('translator')->get('admin::app.components.modal.confirm.disagree-btn'); ?>",
                        btnAgree: "<?php echo app('translator')->get('admin::app.components.modal.confirm.agree-btn'); ?>",
                    },
                    agree = () => {},
                    disagree = () => {},
                }) {
                    this.isOpen = true;

                    document.body.style.overflow = 'hidden';

                    this.title = title;

                    this.message = message;

                    this.options = options;

                    this.agreeCallback = agree;

                    this.disagreeCallback = disagree;
                },

                disagree() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    this.disagreeCallback();
                },

                agree() {
                    this.isOpen = false;

                    document.body.style.overflow = 'auto';

                    this.agreeCallback();
                },

                registerGlobalEvents() {
                    this.$emitter.on('open-confirm-modal', this.open);
                },
            }
        });
    </script>
<?php $__env->stopPush(); endif; ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm\laravel-crm\packages\Webkul\Admin\src/resources/views/components/modal/confirm.blade.php ENDPATH**/ ?>