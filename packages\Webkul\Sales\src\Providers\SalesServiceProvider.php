<?php

namespace Webkul\Sales\Providers;

use Illuminate\Support\ServiceProvider;

class SalesServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        $this->loadMigrationsFrom(__DIR__.'/../Database/Migrations');

        // Load routes only if the admin middleware is available
        if (app()->bound('router')) {
            $this->loadRoutesFrom(__DIR__.'/../Http/routes.php');
        }

        $this->loadTranslationsFrom(__DIR__.'/../Resources/lang', 'sales');

        $this->publishes([
            __DIR__.'/../Resources/lang' => $this->app->langPath('vendor/sales'),
        ]);

        $this->loadViewsFrom(__DIR__.'/../Resources/views', 'sales');

        $this->publishes([
            __DIR__.'/../Resources/views' => $this->app->resourcePath('views/vendor/sales'),
        ]);
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->registerConfig();

        $this->registerModels();

        $this->registerCommands();
    }

    /**
     * Register package config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->mergeConfigFrom(
            dirname(__DIR__).'/Config/menu.php', 'menu.admin'
        );

        $this->mergeConfigFrom(
            dirname(__DIR__).'/Config/acl.php', 'acl'
        );
    }

    /**
     * Register models.
     *
     * @return void
     */
    protected function registerModels()
    {
        // Temporarily disable model bindings to avoid proxy issues
        // $this->app->bind(\Webkul\Sales\Contracts\SalesTarget::class, \Webkul\Sales\Models\SalesTargetProxy::class);
        // $this->app->bind(\Webkul\Sales\Contracts\SalesAchievement::class, \Webkul\Sales\Models\SalesAchievementProxy::class);
        // $this->app->bind(\Webkul\Sales\Contracts\SalesTeam::class, \Webkul\Sales\Models\SalesTeamProxy::class);
        // $this->app->bind(\Webkul\Sales\Contracts\SalesTeamMember::class, \Webkul\Sales\Models\SalesTeamMemberProxy::class);
    }

    /**
     * Register commands.
     *
     * @return void
     */
    protected function registerCommands()
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Webkul\Sales\Console\Commands\SeedSampleData::class,
            ]);
        }
    }
}
