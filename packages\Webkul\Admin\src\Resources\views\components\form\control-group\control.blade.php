@props([
    'type' => 'text',
    'name' => '',
])

@switch($type)
    @case('hidden')
    @case('text')
    @case('email')
    @case('password')
    @case('number')
        <v-field
            v-slot="{ field, errors }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']) }}
            name="{{ $name }}"
        >
            <input
                type="{{ $type }}"
                name="{{ $name }}"
                v-bind="field"
                :class="[errors.length ? 'border !border-red-600 hover:border-red-600' : '']"
                @php
                    $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']);
                    foreach ($filteredAttributes as $key => $value) {
                        if (is_array($value)) {
                            $filteredAttributes = $filteredAttributes->except($key);
                        }
                    }
                @endphp
                {{ $filteredAttributes->merge(['class' => 'w-full rounded border border-gray-200 px-2.5 py-2 text-sm font-normal text-gray-800 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400']) }}
            />
        </v-field>

        @break

    @case('price')
        <v-field
            v-slot="{ field, errors }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']) }}
            name="{{ $name }}"
        >
            <div
                class="flex w-full items-center overflow-hidden rounded-md border text-sm text-gray-600 transition-all focus-within:border-gray-400 hover:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400"
                :class="[errors.length ? 'border !border-red-600 hover:border-red-600' : '']"
            >
                @if (isset($currency))
                    <span {{ $currency->attributes->merge(['class' => 'py-2.5 text-gray-500 ltr:pl-4 rtl:pr-4']) }}>
                        {{ $currency }}
                    </span>
                @else
                    <span class="py-2.5 text-gray-500 ltr:pl-4 rtl:pr-4">
                        {{ config('app.currency') }}
                    </span>
                @endif

                <input
                    type="text"
                    name="{{ $name }}"
                    v-bind="field"
                    :class="[errors.length ? 'border !border-red-600 hover:border-red-600' : '']"
                    @php
                        $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']);
                        foreach ($filteredAttributes as $key => $value) {
                            if (is_array($value)) {
                                $filteredAttributes = $filteredAttributes->except($key);
                            }
                        }
                    @endphp
                    {{ $filteredAttributes->merge(['class' => 'w-full p-2.5 text-sm text-gray-600 dark:bg-gray-900 dark:text-gray-300']) }}
                />
            </div>
        </v-field>

        @break

    @case('file')
        <v-field
            v-slot="{ field, errors, handleChange, handleBlur }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']) }}
            name="{{ $name }}"
        >
            <input
                type="{{ $type }}"
                v-bind="{ name: field.name }"
                :class="[errors.length ? 'border !border-red-600 hover:border-red-600' : '']"
                @php
                    $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']);
                    foreach ($filteredAttributes as $key => $value) {
                        if (is_array($value)) {
                            $filteredAttributes = $filteredAttributes->except($key);
                        }
                    }
                @endphp
                {{ $filteredAttributes->merge(['class' => 'w-full rounded border border-gray-200 px-2.5 py-2 text-sm font-normal text-gray-800 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400']) }}
                @change="handleChange"
                @blur="handleBlur"
            />
        </v-field>

        @break

    @case('color')
        <v-field
            name="{{ $name }}"
            v-slot="{ field, errors }"
            {{ $attributes->except('class') }}
        >
            <input
                type="{{ $type }}"
                :class="[errors.length ? 'border border-red-500' : '']"
                v-bind="field"
                @php
                    $filteredAttributes = $attributes->except(['value']);
                    foreach ($filteredAttributes as $key => $value) {
                        if (is_array($value)) {
                            $filteredAttributes = $filteredAttributes->except($key);
                        }
                    }
                @endphp
                {{ $filteredAttributes->merge(['class' => 'w-full appearance-none rounded-md border text-sm text-gray-600 transition-all hover:border-gray-400 dark:text-gray-300 dark:hover:border-gray-400']) }}
            >
        </v-field>
        @break

    @case('textarea')
        <v-field
            v-slot="{ field, errors }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']) }}
            name="{{ $name }}"
        >
            <textarea
                type="{{ $type }}"
                name="{{ $name }}"
                v-bind="field"
                :class="[errors.length ? 'border !border-red-600 hover:border-red-600' : '']"
                @php
                    $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']);
                    foreach ($filteredAttributes as $key => $value) {
                        if (is_array($value)) {
                            $filteredAttributes = $filteredAttributes->except($key);
                        }
                    }
                @endphp
                {{ $filteredAttributes->merge(['class' => 'w-full rounded border border-gray-200 px-2.5 py-2 text-sm font-normal text-gray-800 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400']) }}
            >
            </textarea>

            @if ($attributes->get('tinymce', false) || $attributes->get(':tinymce', false))
                <x-admin::tinymce 
                    :selector="'textarea#' . $attributes->get('id')"
                    ::field="field"
                />
            @endif
        </v-field>

        @break

    @case('date')
        <v-field
            v-slot="{ field, errors }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label'])->merge(['rules' => 'regex:^\d{4}-\d{2}-\d{2}$']) }}
            name="{{ $name }}"
        >
            <x-admin::flat-picker.date>
                <input
                    name="{{ $name }}"
                    v-bind="field"
                    :class="[errors.length ? 'border !border-red-600 hover:border-red-600' : '']"
                    @php
                        $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']);
                        foreach ($filteredAttributes as $key => $value) {
                            if (is_array($value)) {
                                $filteredAttributes = $filteredAttributes->except($key);
                            }
                        }
                    @endphp
                    {{ $filteredAttributes->merge(['class' => 'w-full rounded border border-gray-200 px-2.5 py-2 text-sm font-normal text-gray-800 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400']) }}
                    autocomplete="off"
                />
            </x-admin::flat-picker.date>
        </v-field>

        @break

    @case('datetime')
        <v-field
            v-slot="{ field, errors }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label'])->merge(['rules' => 'regex:^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$']) }}
            name="{{ $name }}"
        >
            <x-admin::flat-picker.datetime>
                <input
                    name="{{ $name }}"
                    v-bind="field"
                    :class="[errors.length ? 'border !border-red-600 hover:border-red-600' : '']"
                    @php
                        $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']);
                        foreach ($filteredAttributes as $key => $value) {
                            if (is_array($value)) {
                                $filteredAttributes = $filteredAttributes->except($key);
                            }
                        }
                    @endphp
                    {{ $filteredAttributes->merge(['class' => 'w-full rounded border border-gray-200 px-2.5 py-2 text-sm font-normal text-gray-800 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400']) }}
                    autocomplete="off"
                >
            </x-admin::flat-picker.datetime>
        </v-field>
        @break

    @case('select')
        <v-field
            v-slot="{ field, errors }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']) }}
            name="{{ $name }}"
        >
            <select
                name="{{ $name }}"
                v-bind="field"
                :class="[errors.length ? 'border border-red-500' : '']"
                @php
                    $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label']);
                    foreach ($filteredAttributes as $key => $value) {
                        if (is_array($value)) {
                            $filteredAttributes = $filteredAttributes->except($key);
                        }
                    }
                @endphp
                {{ $filteredAttributes->merge(['class' => 'custom-select w-full rounded border border-gray-200 px-2.5 py-2 text-sm font-normal text-gray-800 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400']) }}
            >
                {{ $slot }}
            </select>
        </v-field>

        @break

    @case('multiselect')
        <v-field
            as="select"
            v-slot="{ value }"
            :class="[errors && errors['{{ $name }}'] ? 'border !border-red-600 hover:border-red-600' : '']"
            @php
                $filteredAttributes = $attributes->except([]);
                foreach ($filteredAttributes as $key => $value) {
                    if (is_array($value)) {
                        $filteredAttributes = $filteredAttributes->except($key);
                    }
                }
            @endphp
            {{ $filteredAttributes->merge(['class' => 'flex w-full flex-col rounded-md border bg-white px-3 py-2.5 text-sm font-normal text-gray-600 transition-all hover:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400']) }}
            name="{{ $name }}"
            multiple
        >
            {{ $slot }}
        </v-field>

        @break

    @case('checkbox')
        <v-field
            v-slot="{ field }"
            type="checkbox"
            class="hidden"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label', 'key', ':key']) }}
            name="{{ $name }}"
        >
            <input
                type="checkbox"
                name="{{ $name }}"
                v-bind="field"
                class="peer sr-only"
                @php
                    $filteredAttributes = $attributes->except(['rules', 'label', ':label', 'key', ':key']);
                    foreach ($filteredAttributes as $key => $value) {
                        if (is_array($value)) {
                            $filteredAttributes = $filteredAttributes->except($key);
                        }
                    }
                @endphp
                {{ $filteredAttributes }}
            />

            <v-checked-handler
                :field="field"
                checked="{{ $attributes->get('checked') }}"
            >
            </v-checked-handler>
        </v-field>

        <label
             @php
                $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label', 'key', ':key']);
                foreach ($filteredAttributes as $key => $value) {
                    if (is_array($value)) {
                        $filteredAttributes = $filteredAttributes->except($key);
                    }
                }
            @endphp
            {{ $filteredAttributes->merge(['class' => 'text-gray-500 icon-checkbox-outline peer-checked:icon-checkbox-select text-2xl peer-checked:text-blue-600'])->merge(['class' => $attributes->get('disabled') ? 'cursor-not-allowed opacity-70' : 'cursor-pointer']) }}
        >
        </label>

        @break

    @case('radio')
        <v-field
            type="radio"
            class="hidden"
            v-slot="{ field }"
            {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label', 'key', ':key']) }}
            name="{{ $name }}"
        >
            <input
                type="radio"
                name="{{ $name }}"
                v-bind="field"
                @php
                    $filteredAttributes = $attributes->except(['rules', ':label', 'key', ':key']);
                    foreach ($filteredAttributes as $key => $value) {
                        if (is_array($value)) {
                            $filteredAttributes = $filteredAttributes->except($key);
                        }
                    }
                @endphp
                {{ $filteredAttributes->merge(['class' => 'peer sr-only']) }}
            />
                
            <v-checked-handler
                class="hidden"
                :field="field"
                checked="{{ $attributes->get('checked') }}"
            >
            </v-checked-handler>
        </v-field>

        <label
            @php
                $filteredAttributes = $attributes->except(['value', ':value', 'v-model', 'rules', ':rules', 'label', ':label', 'key', ':key']);
                foreach ($filteredAttributes as $key => $value) {
                    if (is_array($value)) {
                        $filteredAttributes = $filteredAttributes->except($key);
                    }
                }
            @endphp
            {{ $filteredAttributes->merge(['class' => 'icon-radio-normal peer-checked:icon-radio-selected cursor-pointer text-2xl peer-checked:text-blue-600']) }}
        >
        </label>

        @break

    @case('switch')
        <label class="relative inline-flex cursor-pointer items-center">
            <v-field
                type="checkbox"
                class="hidden"
                v-slot="{ field }"
                {{ $attributes->only(['name', ':name', 'value', ':value', 'v-model', 'rules', ':rules', 'label', ':label', 'key', ':key']) }}
                name="{{ $name }}"
            >
                <input
                    type="checkbox"
                    name="{{ $name }}"
                    id="{{ $name }}"
                    class="peer sr-only"
                    v-bind="field"
                    @php
                        $filteredAttributes = $attributes->except(['v-model', 'rules', ':rules', 'label', ':label', 'key', ':key']);
                        foreach ($filteredAttributes as $key => $value) {
                            if (is_array($value)) {
                                $filteredAttributes = $filteredAttributes->except($key);
                            }
                        }
                    @endphp
                    {{ $filteredAttributes }}
                />
                
                <v-checked-handler
                    class="hidden"
                    :field="field"
                    checked="{{ $attributes->get('checked') }}"
                >
                </v-checked-handler>
            </v-field>

            <label
                class="peer h-5 w-9 cursor-pointer rounded-full bg-gray-200 after:absolute after:top-0.5 after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-blue-300 dark:bg-gray-800 dark:after:border-white dark:after:bg-white dark:peer-checked:bg-gray-950 after:ltr:left-0.5 peer-checked:after:ltr:translate-x-full after:rtl:right-0.5 peer-checked:after:rtl:-translate-x-full"
                for="{{ $name }}"
            ></label>
        </label>

        @break

    @case('image')
        <x-admin::media.images
            name="{{ $name }}"
            ::class="[errors && errors['{{ $name }}'] ? 'border !border-red-600 hover:border-red-600' : '']"
            @php
                $filteredAttributes = $attributes->except([]);
                foreach ($filteredAttributes as $key => $value) {
                    if (is_array($value)) {
                        $filteredAttributes = $filteredAttributes->except($key);
                    }
                }
            @endphp
            {{ $filteredAttributes }}
        />

        @break
    
    @case('inline')
        <x-admin::form.control-group.controls.inline.text 
            @php
                $filteredAttributes = $attributes->except([]);
                foreach ($filteredAttributes as $key => $value) {
                    if (is_array($value)) {
                        $filteredAttributes = $filteredAttributes->except($key);
                    }
                }
            @endphp
            {{ $filteredAttributes }}
        />

        @break

    @case('custom')
        <v-field 
            @php
                $filteredAttributes = $attributes->except([]);
                foreach ($filteredAttributes as $key => $value) {
                    if (is_array($value)) {
                        $filteredAttributes = $filteredAttributes->except($key);
                    }
                }
            @endphp
            {{ $filteredAttributes }}
        >
            {{ $slot }}
        </v-field>

        @break

    @case('tags')
        <x-admin::form.control-group.controls.tags
            :name="$name"
            :data="$attributes->get(':data') ?? $attributes->get('data')"
            @php
                $filteredAttributes = $attributes->except([]);
                foreach ($filteredAttributes as $key => $value) {
                    if (is_array($value)) {
                        $filteredAttributes = $filteredAttributes->except($key);
                    }
                }
            @endphp
            {{ $filteredAttributes }}
        />
        @break
@endswitch

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-checked-handler-template"
    >
    </script>

    <script type="module">
        app.component('v-checked-handler', {
            template: '#v-checked-handler-template',

            props: ['field', 'checked'],

            mounted() {
                if (this.checked == '') {
                    return;
                }

                this.field.checked = true;

                this.field.onChange();
            },
        });
    </script>
@endpushOnce>
