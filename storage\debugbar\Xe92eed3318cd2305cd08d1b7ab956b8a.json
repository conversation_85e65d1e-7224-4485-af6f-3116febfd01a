{"__meta": {"id": "Xe92eed3318cd2305cd08d1b7ab956b8a", "datetime": "2025-07-09 11:05:37", "utime": 1752039337.749323, "method": "GET", "uri": "/cache/logo/bagisto.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.25", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[11:05:37] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm\\laravel-crm\\packages\\Webkul\\Installer\\src\\Http\\Controllers\\ImageCacheController.php on line 104", "message_html": null, "is_string": false, "label": "warning", "time": 1752039337.742094, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752039335.666429, "end": 1752039337.74935, "duration": 2.082921028137207, "duration_str": "2.08s", "measures": [{"label": "Booting", "start": 1752039335.666429, "relative_start": 0, "end": 1752039336.130993, "relative_end": 1752039336.130993, "duration": 0.46456384658813477, "duration_str": "465ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752039336.131016, "relative_start": 0.4645869731903076, "end": 1752039337.749352, "relative_end": 1.9073486328125e-06, "duration": 1.6183359622955322, "duration_str": "1.62s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24323896, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cache/{filename}", "as": "image_cache", "controller": "Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fhally%2FOneDrive%2FDesktop%2FCRM%2Flaravel-crm%2Flaravel-crm%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHttp%2FControllers%2FImageCacheController.php&line=30\" onclick=\"\">packages/Webkul/Installer/src/Http/Controllers/ImageCacheController.php:30-41</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IetemKgSeHEoaHqvMZuYHbyoKJd2G8BumXSisNcd", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/cache/logo/bagisto.png", "status_code": "<pre class=sf-dump id=sf-dump-1501769672 data-indent-pad=\"  \"><span class=sf-dump-num>304</span>\n</pre><script>Sfdump(\"sf-dump-1501769672\", {\"maxDepth\":0})</script>\n", "status_text": "Not Modified", "format": "html", "content_type": "text/html", "request_query": "<pre class=sf-dump id=sf-dump-395719904 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-395719904\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1367070153 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1367070153\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1043955289 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"716 characters\">XSRF-TOKEN=eyJpdiI6IlVUdEp2R3U0UldRZVJBbldYdXpWZFE9PSIsInZhbHVlIjoidWJLVXBQVlduKzlOOUo3WmdSK3BpRFoyMENDZkdDQjNqTUlVa3BpbVFlWWhwUDZoQnQyYnZ3Rk5rb3JQejBteURKRGc1UlhnajNldlN0emorbVVEb0Q4eGJwejZ3dE1lOFB2T2o0UUpOV3I4VnFIY2dJN3FNNDY2MFZWKzVzR1MiLCJtYWMiOiJmYWE4YzE5MzM1N2RlMmQ2ZTM1NmZlY2Y0YjZkZjg1MzhjZDZiMzY3YjQyNTFmNzI1MGE4OGRkMGM5M2UxYmUyIiwidGFnIjoiIn0%3D; krayin_crm_session=eyJpdiI6ImJObkg0UzBOdjE2cTc2SkhCSnpyT2c9PSIsInZhbHVlIjoiRkMrUXZ2elR6NzhmeU10aVFzNWsweTVGbXFTQjBGYWxRK1JNVmpIMklGZVh6S3VhNXlhSHhQbEtZbVNGMVNGUStBbC9aR1o1OWs1dUNHTlFNbWtyOVZIUHBqMXNNbDVEZ3E0UUtRbFd3T0R3OG1uZmVFUXdzdldvSUFtSW1FbmwiLCJtYWMiOiI3MDU3ZGQwZDk5MGQ5N2Y5YTkzMzRiZDRjNDQ1ZDU3ZWFiZDc0MTg1OTUyNTA4YjgzY2U5ZjE3YTdmNDM4OWZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-none-match</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043955289\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1168723503 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlVUdEp2R3U0UldRZVJBbldYdXpWZFE9PSIsInZhbHVlIjoidWJLVXBQVlduKzlOOUo3WmdSK3BpRFoyMENDZkdDQjNqTUlVa3BpbVFlWWhwUDZoQnQyYnZ3Rk5rb3JQejBteURKRGc1UlhnajNldlN0emorbVVEb0Q4eGJwejZ3dE1lOFB2T2o0UUpOV3I4VnFIY2dJN3FNNDY2MFZWKzVzR1MiLCJtYWMiOiJmYWE4YzE5MzM1N2RlMmQ2ZTM1NmZlY2Y0YjZkZjg1MzhjZDZiMzY3YjQyNTFmNzI1MGE4OGRkMGM5M2UxYmUyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>krayin_crm_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImJObkg0UzBOdjE2cTc2SkhCSnpyT2c9PSIsInZhbHVlIjoiRkMrUXZ2elR6NzhmeU10aVFzNWsweTVGbXFTQjBGYWxRK1JNVmpIMklGZVh6S3VhNXlhSHhQbEtZbVNGMVNGUStBbC9aR1o1OWs1dUNHTlFNbWtyOVZIUHBqMXNNbDVEZ3E0UUtRbFd3T0R3OG1uZmVFUXdzdldvSUFtSW1FbmwiLCJtYWMiOiI3MDU3ZGQwZDk5MGQ5N2Y5YTkzMzRiZDRjNDQ1ZDU3ZWFiZDc0MTg1OTUyNTA4YjgzY2U5ZjE3YTdmNDM4OWZhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168723503\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1410987405 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=10080, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">df1bdb0d95767d854463103bf5a03c96</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 05:35:37 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410987405\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1400790715 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IetemKgSeHEoaHqvMZuYHbyoKJd2G8BumXSisNcd</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400790715\", {\"maxDepth\":0})</script>\n"}}